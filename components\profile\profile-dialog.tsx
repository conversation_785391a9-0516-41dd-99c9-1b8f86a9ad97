"use client"

import { useState, useRef, useEffect } from "react"
import { Camera, User, Mail, Upload, Loader2 } from "lucide-react"
import { useSession } from "next-auth/react"
import { toast } from "sonner"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"

interface ProfileDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ProfileDialog({ open, onOpenChange }: ProfileDialogProps) {
  const { data: session, update } = useSession()
  const [isUploading, setIsUploading] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [profileData, setProfileData] = useState({
    name: "",
    surname: "",
    image: null as string | null,
  })
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fetch profile data when dialog opens
  useEffect(() => {
    const fetchProfileData = async () => {
      setIsLoading(true)
      try {
        const response = await fetch('/api/profile')
        if (response.ok) {
          const data = await response.json()
          setProfileData({
            name: data.user.name || "",
            surname: data.user.surname || "",
            image: data.user.image || null,
          })
        } else {
          // Fallback to session data
          setProfileData({
            name: session?.user?.name || "",
            surname: "",
            image: session?.user?.image || null,
          })
        }
      } catch (error) {
        console.error('Failed to fetch profile:', error)
        // Fallback to session data
        setProfileData({
          name: session?.user?.name || "",
          surname: "",
          image: session?.user?.image || null,
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (open && session?.user) {
      fetchProfileData()
    }
  }, [open, session?.user])

  // Generate initials from user's name
  const getInitials = (name?: string) => {
    if (!name) return "U"
    const names = name.split(" ")
    if (names.length >= 2) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase()
    }
    return name.substring(0, 2).toUpperCase()
  }

  const handleImageClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/upload/profile-picture', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Upload failed')
      }

      const data = await response.json()

      // Update local state
      setProfileData(prev => ({ ...prev, image: data.imageUrl }))

      // Update the session with new image and user data
      await update({
        ...session,
        user: {
          ...session?.user,
          name: data.user.name,
          image: data.imageUrl,
        }
      })

      toast.success("Profile picture updated successfully!")

    } catch (error) {
      console.error('Upload error:', error)
      toast.error(error instanceof Error ? error.message : "Failed to upload image")
    } finally {
      setIsUploading(false)
    }
  }

  const updateProfile = async (updates: Partial<typeof profileData>) => {
    setIsUpdating(true)
    try {
      const response = await fetch('/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Update failed')
      }

      const data = await response.json()
      
      // Update the session with new data
      await update({
        ...session,
        user: {
          ...session?.user,
          name: data.user.name,
          image: data.user.image,
        }
      })

      return data.user
    } catch (error) {
      console.error('Profile update error:', error)
      throw error
    } finally {
      setIsUpdating(false)
    }
  }

  const handleSaveProfile = async () => {
    try {
      const updatedUser = await updateProfile({
        name: profileData.name,
        surname: profileData.surname,
      })

      // Update local state with the response
      setProfileData(prev => ({
        ...prev,
        name: updatedUser.name,
        surname: updatedUser.surname,
      }))

      toast.success("Profile updated successfully!")
      onOpenChange(false)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update profile")
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Profile Settings</DialogTitle>
          <DialogDescription>
            Update your profile information and picture.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              {/* Profile Picture Section */}
              <div className="flex flex-col items-center space-y-4">
            <div className="relative group">
              <Avatar className="h-24 w-24 cursor-pointer" onClick={handleImageClick}>
                <AvatarImage 
                  src={profileData.image || undefined} 
                  alt={session?.user?.name || "Profile"} 
                />
                <AvatarFallback className="text-lg">
                  {getInitials(session?.user?.name)}
                </AvatarFallback>
              </Avatar>
              
              {/* Upload overlay */}
              <div 
                className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                onClick={handleImageClick}
              >
                {isUploading ? (
                  <Loader2 className="h-6 w-6 text-white animate-spin" />
                ) : (
                  <Camera className="h-6 w-6 text-white" />
                )}
              </div>
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleImageClick}
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Change Picture
                </>
              )}
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>

          <Separator />

          {/* Profile Information */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">First Name</Label>
                <Input
                  id="name"
                  value={profileData.name}
                  onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter your first name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="surname">Last Name</Label>
                <Input
                  id="surname"
                  value={profileData.surname}
                  onChange={(e) => setProfileData(prev => ({ ...prev, surname: e.target.value }))}
                  placeholder="Enter your last name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Email</Label>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span>{session?.user?.email}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveProfile} disabled={isUpdating}>
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
